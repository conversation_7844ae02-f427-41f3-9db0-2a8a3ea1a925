// app.module.ts
import { Mo<PERSON>le, OnModuleInit } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MongoModule } from './mongo-module/mongo.module';
import { LiveMongoDriver } from './mongo-module/mongo.provider';
import { TestEventModule } from './test-event-service/kafka.module';

@Module({
  imports: [
    MongoModule,
    TestEventModule,
    ConfigModule.forRoot({
      envFilePath: ['.env.local', '.env'],
      isGlobal: true,
    }),
  ],
})
export class AppModule implements OnModuleInit {
  async onModuleInit() {
  }
}
