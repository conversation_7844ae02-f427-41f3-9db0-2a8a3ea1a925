import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MongoClient, Db, Collection, Document } from 'mongodb';

@Injectable()
export class LiveMongoDriver implements OnModuleInit, OnModuleDestroy {
  private static cachedClient: MongoClient | null = null;
  private static cachedDb: Db | null = null;

  private client: MongoClient;
  private db: Db;
  private isConnected = false;

  constructor(private configService: ConfigService) {
    const uri = this.configService.get<string>('MONGODB_URI')!;
    const dbName = this.configService.get<string>('MONGODB_DB') || 'test';

    if (!LiveMongoDriver.cachedClient) {
      LiveMongoDriver.cachedClient = new MongoClient(uri);
    }
    this.client = LiveMongoDriver.cachedClient;

    if (LiveMongoDriver.cachedDb) {
      this.db = LiveMongoDriver.cachedDb;
      this.isConnected = true;
    } else {
      this.db = this.client.db(dbName);
    }
  }

  async onModuleInit() {
    await this.connect();
  }

  async connect(): Promise<void> {
    if (!this.isConnected) {
      try {
        await this.client.db().command({ ping: 1 });
      } catch {
        await this.client.connect();
      }

      const dbName = this.configService.get<string>('MONGODB_DB') || 'test';
      this.db = this.client.db(dbName);
      LiveMongoDriver.cachedDb = this.db;
      this.isConnected = true;

      console.log('[LiveMongoDriver] Connected to MongoDB:', dbName);
    }
  }

  getCollection<T extends Document = Document>(name: string): Collection<T> {
    if (!this.isConnected) {
      throw new Error('Mongo client is not connected yet. Call connect() first.');
    }
    return this.db.collection<T>(name);
  }

  async onModuleDestroy() {
    await this.close();
  }

  async close(): Promise<void> {
    if (this.isConnected) {
      await this.client.close();
      this.isConnected = false;
      LiveMongoDriver.cachedClient = null;
      LiveMongoDriver.cachedDb = null;
      console.log('[LiveMongoDriver] MongoDB connection closed');
    }
  }
}
