import { Injectable, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Kafka, EachMessagePayload, Consumer } from 'kafkajs';
import {
  FinalResultAvailableEvent,
  NewSessionStartedEvent,
  ReviewRequiredEvent,
  TestAppEventHandler,
  TestCompletedEvent,
  TestSessionCompletedEvent,
  TestUpdatedEvent,
  WeeklyRecommendationEvent
} from './test-events';
import { Collection, ObjectId } from 'mongodb';
import { MONGO_COLLECTIONS } from 'src/mongo-module/mongoCollections';
import { LiveMongoDriver } from 'src/mongo-module/mongo.provider';

@Injectable()
export class KafkaConsumerService implements OnModuleInit {
  private kafka: Kafka;
  private consumer: Consumer;

  constructor(private readonly configService: ConfigService, private readonly mongo: LiveMongoDriver) {
    const brokers = this.configService.get<string>('KAFKA_BROKER')?.split(',') || [];
    this.kafka = new Kafka({
      clientId: 'nestjs-kafka-client',
      brokers,
    });
    this.consumer = this.kafka.consumer({ groupId: 'nestjs-group' });
  }

  private readonly testTakerHandlers: TestAppEventHandler[] = [
    new TestCompletedEvent(),
    new FinalResultAvailableEvent(),
    new WeeklyRecommendationEvent()
  ]

  private readonly creatorHandlers: TestAppEventHandler[] = [
    new NewSessionStartedEvent(),
    new TestUpdatedEvent(),
    new TestSessionCompletedEvent(),
    new ReviewRequiredEvent()
  ]

  private readonly handlers = [
    ...this.testTakerHandlers,
    ...this.creatorHandlers
  ]

  private readonly handlersMap = new Map<string, TestAppEventHandler>(
    this.handlers.map(handler => [handler.type, handler])
  );

  async onModuleInit() {
    await this.consumer.connect();
    await this.consumer.subscribe({ topic: 'test-events', fromBeginning: true });
    const eventStoreCollection = this.mongo.getCollection(MONGO_COLLECTIONS.EVENT_STORE);

    await this.consumer.run({
      eachMessage: async ({ topic, partition, message }: EachMessagePayload) => {
        const value = message.value?.toString();

        if (!value) {
          return;
        }

        const messageParsed: { event: string, type: string, timestamp: string } = JSON.parse(value);
        const event = await eventStoreCollection.findOne({ _id: new ObjectId(messageParsed.event) });

        if (!event) {
          console.log(`[Kafka] Event not found for id ${value}`);
          return;
        }

        const handler = this.handlersMap.get(event.type);

        if (!handler) {
          console.log(`[Kafka] No handler found for event type ${event.type}`);
          console.log(this.handlersMap)
          return;
        }

        console.log(`[Kafka] Message received on topic "${topic}": ${value}`);

        await handler.handle({
          payload: event.payload
        }, event.timestamp);
      },
    });

    console.log('[Kafka] Consumer connected and listening...');
  }
}
