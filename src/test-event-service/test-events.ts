export abstract class TestEvent<TPayload> {
  abstract type: string;

  abstract handle(event: { payload: TPayload }, timestamp: Date): Promise<void>;
}

export class TestUpdatedEvent extends TestEvent<{ test_id: string }> {
  type = 'test_updated';

  async handle(event: { payload: { test_id: string } }, timestamp: Date): Promise<void> {
    console.log(`[TestUpdated] Test ${event.payload.test_id} updated at ${timestamp}`);
  }
}

export class TestCompletedEvent extends TestEvent<{ user_id: string; test_id: string }> {
  type = 'test_completed';

  async handle(event: { payload: { user_id: string; test_id: string } }, timestamp: Date): Promise<void> {
    console.log(`[TestCompleted] User ${event.payload.user_id} completed test ${event.payload.test_id} at ${timestamp}`);
  }
}

export class FinalResultAvailableEvent extends TestEvent<{ user_id: string; test_id: string }> {
  type = 'final_result_available';

  async handle(event: { payload: { user_id: string; test_id: string } }, timestamp: Date): Promise<void> {
    console.log(`[ResultAvailable] Final result ready for User ${event.payload.user_id} on test ${event.payload.test_id}`);
  }
}

export class WeeklyRecommendationEvent extends TestEvent<{ user_id: string; test_id: string }> {
  type = 'test_recommendation';

  async handle(event: { payload: { user_id: string; test_id: string } }, timestamp: Date): Promise<void> {
    console.log(`[Recommendation] Recommend test ${event.payload.test_id} to user ${event.payload.user_id}`);
  }
}

export class NewSessionStartedEvent extends TestEvent<{ test_id: string; user_id: string; ownerId: string }> {
  type = 'new_session_started';

  async handle(event: { payload: { test_id: string; user_id: string; ownerId: string } }, timestamp: Date): Promise<void> {
    console.log(`[NewSession] User ${event.payload.user_id} started session for test ${event.payload.test_id} (Owner: ${event.payload.ownerId})`);
  }
}

export class TestSessionCompletedEvent extends TestEvent<{ sessionId: string; test_id: string; user_id: string; ownerId: string }> {
  type = 'test_session_completed';

  async handle(event: { payload: { sessionId: string; test_id: string; user_id: string; ownerId: string } }, timestamp: Date): Promise<void> {
    console.log(`[SessionCompleted] User ${event.payload.user_id} completed session ${event.payload.sessionId} for test ${event.payload.test_id}`);
  }
}

export class ReviewRequiredEvent extends TestEvent<{ sessionId: string; test_id: string; ownerId: string }> {
  type = 'review_required';

  async handle(event: { payload: { sessionId: string; test_id: string; ownerId: string } }, timestamp: Date): Promise<void> {
    console.log(`[ReviewRequired] Owner ${event.payload.ownerId} needs to review session ${event.payload.sessionId} (Test: ${event.payload.test_id})`);
  }
}

// ✅ Union type for all handlers
export type TestAppEventHandler =
  | TestUpdatedEvent
  | TestCompletedEvent
  | FinalResultAvailableEvent
  | WeeklyRecommendationEvent
  | NewSessionStartedEvent
  | TestSessionCompletedEvent
  | ReviewRequiredEvent;
